spec:
  inputs:
    stage:
      default: deploy
    image:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/java-17-node-build-image:latest
    application-version:
    template-project:
      default: dfe-platform-frontend

---
.update-helm-chart:
  stage: deploy
  image: $[[ inputs.image ]]
  variables:
    HELM_CHARTS_GROUP_NAME: 'dfe'
    HELM_CHARTS_PROJECT_NAME: 'dfe-application-set'
    HELM_CHARTS_REPO: 'https://pipeline-token:${APPLICATION_SET_ACCESS_TOKEN}@gitlab.dachser.com/dachser/business-integration/dachser-platform/${HELM_CHARTS_GROUP_NAME}/${HELM_CHARTS_PROJECT_NAME}.git'
    DEPLOYMENT_ENVIRONMENT: 'dev'
  script:
    - |
      case $DEPLOYMENT_ENVIRONMENT in

        test)
          export VALUES_FILE=values-test.yaml
          ;;

        prod)
          export VALUES_FILE=values-prod.yaml
          ;;

        *)
          export VALUES_FILE=values.yaml
          ;;
      esac
    - git clone $HELM_CHARTS_REPO
    - ls -la
    - cd ${HELM_CHARTS_PROJECT_NAME}
    - ls -la
    - echo "Looking for directory:${CI_PROJECT_NAME}"
    - ls -la ${CI_PROJECT_NAME} || echo "Directory ${CI_PROJECT_NAME} not found"
    - |
      if [ ! -d "${CI_PROJECT_NAME}" ]; then
        echo "Creating directory ${CI_PROJECT_NAME} from $[[ inputs.template-project ]] template"

        # Check if template directory exists
        if [ ! -d "$[[ inputs.template-project ]]" ]; then
          echo "ERROR: Template directory '$[[ inputs.template-project ]]' does not exist!"
          echo "Available directories:"
          ls -la
          exit 1
        fi

        # Copy template directory
        cp -r $[[ inputs.template-project ]] ${CI_PROJECT_NAME}

        # Verify the copy was successful
        if [ ! -d "${CI_PROJECT_NAME}" ]; then
          echo "ERROR: Failed to create directory ${CI_PROJECT_NAME}"
          exit 1
        fi

        echo "Directory created successfully"
        ls -la ${CI_PROJECT_NAME}
      fi
    - |
      # Ensure directory exists before changing to it
      if [ ! -d "${CI_PROJECT_NAME}" ]; then
        echo "ERROR: Directory ${CI_PROJECT_NAME} still does not exist after creation attempt"
        exit 1
      fi
    - cd ${CI_PROJECT_NAME}
    - pwd
    - ls -la
    - |
      echo "updating tag and app version to $[[ inputs.application-version ]] and ${IMAGE_DIGEST}"
      sed -i "s#tag: \"\(.*\)\"#tag: \"$[[ inputs.application-version ]]\"#" ${VALUES_FILE}
      sed -i "s#digets: \"\(.*\)\"#digets: \"${IMAGE_DIGEST}\"#" ${VALUES_FILE}

      echo "updating revision to gitHash ${CI_COMMIT_SHA}"
      sed -i "s#revision: \"\(.*\)\"#revision: \"${CI_COMMIT_SHA}\"#" ${VALUES_FILE}
    - cd ..
    - git config user.email "<EMAIL>"
    - git config user.name "platform-ci-bot"
    - git add .
    - 'git commit -m "Update ${CI_PROJECT_NAME} - ${DEPLOYMENT_ENVIRONMENT}" -m "Hash: ${CI_COMMIT_SHA}"'
    - git push origin HEAD:master --force-with-lease

update-helm-chart-dev:
  extends: [.update-helm-chart]
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ $ALPHA_PATTERN
  variables:
    DEPLOYMENT_ENVIRONMENT: dev

update-helm-chart-test:
  extends: [ .update-helm-chart ]
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ $BETA_PATTERN
  variables:
    DEPLOYMENT_ENVIRONMENT: test

update-helm-chart-prod:
  extends: [ .update-helm-chart ]
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG !~ $BETA_PATTERN && $CI_COMMIT_TAG !~ $ALPHA_PATTERN
  variables:
    DEPLOYMENT_ENVIRONMENT: prod